// Script de teste para verificar a conexão GA4 e MCP Server
const axios = require('axios');
const fs = require('fs');
const path = require('path');

const MCP_URL = 'http://localhost:3000';
const CREDENTIALS_FILE = './credenciais-ga4.json';

async function testCredentials() {
    console.log('🔍 Verificando credenciais...');
    
    if (!fs.existsSync(CREDENTIALS_FILE)) {
        console.error('❌ Arquivo credenciais-ga4.json não encontrado');
        console.log('📝 Crie o arquivo com suas credenciais da Service Account');
        return false;
    }
    
    try {
        const credentials = require(CREDENTIALS_FILE);
        if (!credentials.type || credentials.type !== 'service_account') {
            console.error('❌ Arquivo de credenciais inválido');
            return false;
        }
        
        console.log('✅ Credenciais encontradas');
        console.log(`   Project ID: ${credentials.project_id}`);
        console.log(`   Client Email: ${credentials.client_email}`);
        return true;
    } catch (error) {
        console.error('❌ Erro ao ler credenciais:', error.message);
        return false;
    }
}

async function testMCPServer() {
    console.log('\n🔍 Testando MCP Server...');
    
    try {
        // Teste de health check
        const healthResponse = await axios.get(`${MCP_URL}/health`, {
            timeout: 5000
        });
        
        if (healthResponse.data.status === 'ok') {
            console.log('✅ MCP Server está rodando');
            console.log(`   Timestamp: ${healthResponse.data.timestamp}`);
        }
        
        // Teste de geração de token
        console.log('\n🔑 Testando geração de token...');
        const tokenResponse = await axios.post(`${MCP_URL}/token`, {}, {
            timeout: 10000
        });
        
        if (tokenResponse.data.success) {
            console.log('✅ Token gerado com sucesso');
            console.log(`   Token: ${tokenResponse.data.token.substring(0, 50)}...`);
            return tokenResponse.data.token;
        } else {
            console.error('❌ Falha ao gerar token:', tokenResponse.data.error);
            return null;
        }
        
    } catch (error) {
        if (error.code === 'ECONNREFUSED') {
            console.error('❌ MCP Server não está rodando');
            console.log('💡 Execute: npm start');
        } else {
            console.error('❌ Erro ao conectar com MCP:', error.message);
        }
        return null;
    }
}

async function testGA4Integration(token) {
    console.log('\n📊 Testando integração GA4...');
    
    // Você pode substituir por um Property ID real para teste
    const TEST_PROPERTY_ID = '330715799';
    
    if (TEST_PROPERTY_ID === 'SEU_PROPERTY_ID_AQUI') {
        console.log('⚠️ Property ID não configurado para teste');
        console.log('💡 Edite o arquivo test-connection.js e adicione um Property ID válido');
        return;
    }
    
    try {
        const response = await axios.post(`${MCP_URL}/ga4-data`, {
            propertyId: TEST_PROPERTY_ID,
            dimensions: [{ name: "date" }],
            metrics: [{ name: "activeUsers" }],
            dateRanges: [{ startDate: "7daysAgo", endDate: "today" }]
        }, {
            timeout: 30000
        });
        
        if (response.data.success) {
            console.log('✅ Dados GA4 obtidos com sucesso');
            console.log(`   Registros: ${response.data.data.rows?.length || 0}`);
        } else {
            console.error('❌ Erro ao buscar dados GA4:', response.data.error);
        }
        
    } catch (error) {
        console.error('❌ Erro na integração GA4:', error.message);
    }
}

async function testEvolutionScript() {
    console.log('\n🤖 Testando script da Evolution...');
    
    try {
        // Simula o contexto da Evolution
        const mockContext = {
            secrets: {
                GA4_CREDENTIALS: fs.readFileSync(CREDENTIALS_FILE, 'utf8')
            }
        };
        
        const mockInput = {
            propertyId: 'SEU_PROPERTY_ID_AQUI',
            method: 'mcp',
            mcpUrl: MCP_URL
        };
        
        if (mockInput.propertyId === 'SEU_PROPERTY_ID_AQUI') {
            console.log('⚠️ Property ID não configurado para teste do script Evolution');
            return;
        }
        
        const evolutionScript = require('./from google.js');
        const result = await evolutionScript(mockInput, mockContext);
        
        if (result.success) {
            console.log('✅ Script da Evolution funcionando');
            console.log(`   Fonte: ${result.source}`);
        } else {
            console.error('❌ Erro no script Evolution:', result.error);
        }
        
    } catch (error) {
        console.error('❌ Erro ao testar script Evolution:', error.message);
    }
}

async function runAllTests() {
    console.log('🚀 Iniciando testes do GA4 MCP Server\n');
    
    // Teste 1: Credenciais
    const credentialsOk = await testCredentials();
    if (!credentialsOk) {
        console.log('\n❌ Testes interrompidos - configure as credenciais primeiro');
        return;
    }
    
    // Teste 2: MCP Server
    const token = await testMCPServer();
    if (!token) {
        console.log('\n❌ Testes interrompidos - MCP Server com problemas');
        return;
    }
    
    // Teste 3: Integração GA4
    await testGA4Integration(token);
    
    // Teste 4: Script Evolution
    await testEvolutionScript();
    
    console.log('\n🎉 Testes concluídos!');
    console.log('\n📋 Próximos passos:');
    console.log('   1. Configure um Property ID válido no teste');
    console.log('   2. Use o script from google.js na Evolution API');
    console.log('   3. Mantenha o MCP Server rodando com: npm start');
}

// Executa os testes
if (require.main === module) {
    runAllTests().catch(console.error);
}

module.exports = {
    testCredentials,
    testMCPServer,
    testGA4Integration,
    testEvolutionScript
};
