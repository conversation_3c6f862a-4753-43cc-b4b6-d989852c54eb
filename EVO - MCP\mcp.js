// MCP local em Node.js usando Express
// Esse servidor gera o token e responde as requisições da Evolution

const express = require('express');
const { GoogleAuth } = require('google-auth-library');
const app = express();
const PORT = 3000;

// Carrega as credenciais da Service Account direto do JSON
const serviceAccount = require('./credenciais-ga4.json');

// Endpoint principal que a Evolution vai chamar
app.use(express.json());
app.post('/', async (req, res) => {
    try {
        // Cria o auth client com as credenciais
        const auth = new GoogleAuth({
            credentials: serviceAccount,
            scopes: ['https://www.googleapis.com/auth/analytics.readonly']
        });

        const accessToken = await auth.getAccessToken();

        // Exemplo: devolvendo só o token, você pode adaptar para já buscar os dados no GA4 se quiser
        res.json({
            token: accessToken,
            mensagem: 'Token gerado com sucesso'
        });
    } catch (error) {
        console.error('Erro ao gerar token:', error);
        res.status(500).json({ error: 'Falha ao gerar token' });
    }
});

app.listen(PORT, () => {
    console.log(`MCP rodando localmente em http://localhost:${PORT}`);
});
