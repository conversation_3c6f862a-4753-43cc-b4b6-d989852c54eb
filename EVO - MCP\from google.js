const { GoogleAuth } = require('google-auth-library');
const axios = require('axios');

module.exports = async function (input, context) {
  try {
    // Carrega o JSON de credenciais a partir de um segredo da Evolution
    const serviceAccount = JSON.parse(context.secrets.GA4_CREDENTIALS);

    const auth = new GoogleAuth({
      credentials: serviceAccount,
      scopes: ['https://www.googleapis.com/auth/analytics.readonly']
    });

    const client = await auth.getClient();
    const accessToken = await client.getAccessToken();

    // Monta o corpo da requisição pro GA4
    const requestBody = {
      dimensions: [{ name: "date" }],
      metrics: [{ name: "activeUsers" }],
      dateRanges: [{ startDate: "7daysAgo", endDate: "today" }]
    };

    // Faz o request pro GA4 usando o token
    const response = await axios.post(
      `https://analyticsdata.googleapis.com/v1beta/properties/${input.propertyId}/runReport`,
      requestBody,
      {
        headers: {
          Authorization: `Bearer ${accessToken.token}`,
          'Content-Type': 'application/json'
        }
      }
    );

    // Retorna o resultado da API do GA4 pro agente
    return response.data;

  } catch (error) {
    return { error: error.message };
  }
};
